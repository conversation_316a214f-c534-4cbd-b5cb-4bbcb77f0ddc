#!/bin/bash

# 内部服务器部署脚本 (完整Matrix服务栈)
# 版本: v2.0
# 基于: ESS-HELM 25.6.2 官方稳定版
# 功能: 部署完整的Matrix服务栈，包括Synapse、Element Web、PostgreSQL、MAS等
# 作者: AI
# 日期: 2025-06-20

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ESS_HELM_VERSION="25.6.2"
NAMESPACE="matrix"
RELEASE_NAME="matrix-stack"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 默认配置
DEFAULT_HTTPS_PORT="8443"
DEFAULT_FEDERATION_PORT="8448"
DEFAULT_SERVICE_DIR="$HOME/matrix"
DEFAULT_SSL_MODE="selfsigned"

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "内部服务器部署脚本 (完整Matrix服务栈)"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "必需参数:"
    echo "  --domain DOMAIN              主域名 (例如: matrix.local)"
    echo
    echo "可选参数:"
    echo "  --element-subdomain SUB      Element Web子域名 (默认: element)"
    echo "  --matrix-subdomain SUB       Matrix服务器子域名 (默认: matrix)"
    echo "  --mas-subdomain SUB          MAS认证服务子域名 (默认: mas)"
    echo "  --rtc-subdomain SUB          RTC服务子域名 (默认: rtc)"
    echo "  --turn-subdomain SUB         TURN服务子域名 (默认: turn)"
    echo "  --https-port PORT            HTTPS端口 (默认: 8443)"
    echo "  --federation-port PORT       Matrix联邦端口 (默认: 8448)"
    echo "  --service-dir DIR            服务目录 (默认: ~/matrix)"
    echo "  --ssl-mode MODE              SSL模式 (selfsigned|custom)"
    echo "  --ssl-cert PATH              自定义SSL证书路径"
    echo "  --ssl-key PATH               自定义SSL私钥路径"
    echo "  --internal-network CIDR      内网网段 (默认: 192.168.0.0/16)"
    echo "  -h, --help                   显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 --domain matrix.local"
    echo "  $0 --domain matrix.local --internal-network 10.0.0.0/8"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --domain)
                MAIN_DOMAIN="$2"
                shift 2
                ;;
            --element-subdomain)
                ELEMENT_SUBDOMAIN="$2"
                shift 2
                ;;
            --matrix-subdomain)
                MATRIX_SUBDOMAIN="$2"
                shift 2
                ;;
            --mas-subdomain)
                MAS_SUBDOMAIN="$2"
                shift 2
                ;;
            --rtc-subdomain)
                RTC_SUBDOMAIN="$2"
                shift 2
                ;;
            --turn-subdomain)
                TURN_SUBDOMAIN="$2"
                shift 2
                ;;
            --https-port)
                HTTPS_PORT="$2"
                shift 2
                ;;
            --federation-port)
                FEDERATION_PORT="$2"
                shift 2
                ;;
            --service-dir)
                SERVICE_DIR="$2"
                shift 2
                ;;
            --ssl-mode)
                SSL_MODE="$2"
                shift 2
                ;;
            --ssl-cert)
                SSL_CERT_PATH="$2"
                shift 2
                ;;
            --ssl-key)
                SSL_KEY_PATH="$2"
                shift 2
                ;;
            --internal-network)
                INTERNAL_NETWORK="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查必需参数
    if [[ -z "${MAIN_DOMAIN:-}" ]]; then
        error "缺少必需参数: --domain"
        show_help
        exit 1
    fi
    
    # 设置默认值
    ELEMENT_SUBDOMAIN="${ELEMENT_SUBDOMAIN:-element}"
    MATRIX_SUBDOMAIN="${MATRIX_SUBDOMAIN:-matrix}"
    MAS_SUBDOMAIN="${MAS_SUBDOMAIN:-mas}"
    RTC_SUBDOMAIN="${RTC_SUBDOMAIN:-rtc}"
    TURN_SUBDOMAIN="${TURN_SUBDOMAIN:-turn}"
    HTTPS_PORT="${HTTPS_PORT:-$DEFAULT_HTTPS_PORT}"
    FEDERATION_PORT="${FEDERATION_PORT:-$DEFAULT_FEDERATION_PORT}"
    SERVICE_DIR="${SERVICE_DIR:-$DEFAULT_SERVICE_DIR}"
    SSL_MODE="${SSL_MODE:-$DEFAULT_SSL_MODE}"
    INTERNAL_NETWORK="${INTERNAL_NETWORK:-192.168.0.0/16}"
}

# 验证环境
validate_environment() {
    info "验证内部服务器环境..."
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &>/dev/null; then
        error "无法连接到Kubernetes集群，请检查kubeconfig配置"
        exit 1
    fi
    
    # 检查Helm
    if ! helm version &>/dev/null; then
        error "Helm未安装或配置不正确"
        exit 1
    fi
    
    # 检查Docker
    if ! docker version &>/dev/null; then
        error "Docker未安装或未启动"
        exit 1
    fi
    
    success "环境验证通过"
}

# 创建命名空间
create_namespace() {
    info "创建Kubernetes命名空间: $NAMESPACE"
    
    if kubectl get namespace "$NAMESPACE" &>/dev/null; then
        warning "命名空间 $NAMESPACE 已存在"
    else
        kubectl create namespace "$NAMESPACE"
        success "命名空间 $NAMESPACE 创建成功"
    fi
}

# 下载官方ESS-HELM Chart
download_ess_helm() {
    info "下载官方ESS-HELM Chart (版本: $ESS_HELM_VERSION)..."
    
    local chart_dir="$SERVICE_DIR/ess-helm"
    mkdir -p "$chart_dir"
    
    # 添加官方Helm仓库
    helm repo add element-hq https://element-hq.github.io/ess-helm
    helm repo update
    
    # 下载Chart
    helm pull element-hq/matrix-stack --version "$ESS_HELM_VERSION" --untar --untardir "$chart_dir"
    
    success "ESS-HELM Chart下载完成"
}

# 生成配置文件
generate_configurations() {
    info "生成内部服务器配置文件..."
    
    local config_dir="$SERVICE_DIR/configs"
    mkdir -p "$config_dir"
    
    # 生成主配置文件
    generate_main_values "$config_dir/values.yaml"
    
    # 生成内部服务器配置
    generate_internal_server_config "$config_dir/values-internal-server.yaml"
    
    # 生成网络配置
    generate_network_config "$config_dir/values-network.yaml"
    
    success "配置文件生成完成"
}

# 生成主配置文件
generate_main_values() {
    local file_path="$1"
    
    cat > "$file_path" << EOF
# 内部服务器主配置文件
# 版本: $ESS_HELM_VERSION
# 模式: 内部服务器部署

global:
  serverName: "$MATRIX_SUBDOMAIN.$MAIN_DOMAIN"
  
# Element Web配置
elementWeb:
  enabled: true
  ingress:
    enabled: true
    className: "nginx"
    hosts:
      - host: "$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN"
        paths:
          - path: /
            pathType: Prefix

# Synapse配置
synapse:
  enabled: true
  serverName: "$MATRIX_SUBDOMAIN.$MAIN_DOMAIN"
  publicBaseurl: "https://$MATRIX_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT"
  
  ingress:
    enabled: true
    className: "nginx"
    hosts:
      - host: "$MATRIX_SUBDOMAIN.$MAIN_DOMAIN"
        paths:
          - path: /
            pathType: Prefix
  
  # 联邦配置 (内网环境可选)
  federation:
    enabled: false
    port: $FEDERATION_PORT

# Matrix认证服务配置
matrixAuthenticationService:
  enabled: true
  ingress:
    enabled: true
    className: "nginx"
    hosts:
      - host: "$MAS_SUBDOMAIN.$MAIN_DOMAIN"
        paths:
          - path: /
            pathType: Prefix

# Matrix RTC配置
matrixRtc:
  enabled: true
  ingress:
    enabled: true
    className: "nginx"
    hosts:
      - host: "$RTC_SUBDOMAIN.$MAIN_DOMAIN"
        paths:
          - path: /
            pathType: Prefix

# HAProxy负载均衡器配置
haproxy:
  enabled: true
  
# PostgreSQL数据库配置
postgresql:
  enabled: true
  auth:
    postgresPassword: "$(openssl rand -base64 32)"
    database: "synapse"
  primary:
    persistence:
      enabled: true
      size: 20Gi

# SSL/TLS配置 (内网环境使用自签名证书)
tls:
  mode: "$SSL_MODE"
  $(if [[ "$SSL_MODE" == "custom" ]]; then
    echo "  certFile: \"$SSL_CERT_PATH\""
    echo "  keyFile: \"$SSL_KEY_PATH\""
  fi)

# 内网优化配置
internal:
  enabled: true
  networkPolicy:
    enabled: true
    allowedNetworks:
      - "$INTERNAL_NETWORK"

EOF
}

# 生成内部服务器配置
generate_internal_server_config() {
    local file_path="$1"
    
    cat > "$file_path" << EOF
# 内部服务器专用配置
# 针对内网环境的优化配置

internalServer:
  enabled: true
  
  # 网络配置
  networking:
    publicAccess: false
    internalOnly: true
    allowedNetworks:
      - "$INTERNAL_NETWORK"
      - "10.0.0.0/8"
      - "**********/12"
    
    # 服务类型配置
    serviceType: "ClusterIP"
    
  # 安全配置
  security:
    enableNetworkPolicy: true
    restrictExternalAccess: true
    allowedPorts:
      - $HTTPS_PORT
      - $FEDERATION_PORT
    
  # 性能优化 (内网环境)
  performance:
    enableCaching: true
    cacheSize: "512Mi"
    connectionPooling: true
    maxConnections: 100
    
  # 资源限制 (内网环境适中配置)
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "2Gi"
      cpu: "1000m"
    
  # 监控配置
  monitoring:
    enabled: true
    internal: true
    metrics:
      enabled: true
    logging:
      level: "INFO"
      retention: "7d"

# 存储配置
storage:
  class: "local-path"  # 适用于单节点或小型集群
  size: "50Gi"

# 备份配置
backup:
  enabled: true
  schedule: "0 2 * * *"  # 每天凌晨2点
  retention: "7d"

EOF
}

# 生成网络配置
generate_network_config() {
    local file_path="$1"
    
    cat > "$file_path" << EOF
# 内网网络配置
# 针对内部网络环境的网络策略和配置

networkConfig:
  enabled: true
  
  # 网络策略
  networkPolicy:
    enabled: true
    policyTypes:
      - Ingress
      - Egress
    
    # 入站规则
    ingress:
      - from:
          - namespaceSelector:
              matchLabels:
                name: "$NAMESPACE"
          - podSelector: {}
          - ipBlock:
              cidr: "$INTERNAL_NETWORK"
        ports:
          - protocol: TCP
            port: $HTTPS_PORT
          - protocol: TCP
            port: $FEDERATION_PORT
    
    # 出站规则
    egress:
      - to:
          - namespaceSelector:
              matchLabels:
                name: "kube-system"
      - to:
          - ipBlock:
              cidr: "$INTERNAL_NETWORK"
      - to: []
        ports:
          - protocol: TCP
            port: 53
          - protocol: UDP
            port: 53
  
  # DNS配置
  dns:
    enabled: true
    customDomains:
      - "$MAIN_DOMAIN"
      - "$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN"
      - "$MATRIX_SUBDOMAIN.$MAIN_DOMAIN"
      - "$MAS_SUBDOMAIN.$MAIN_DOMAIN"
      - "$RTC_SUBDOMAIN.$MAIN_DOMAIN"
      - "$TURN_SUBDOMAIN.$MAIN_DOMAIN"
  
  # 负载均衡配置
  loadBalancer:
    enabled: false  # 内网环境不需要外部负载均衡
    type: "ClusterIP"

# TURN服务配置 (内网优化)
turn:
  enabled: true
  config:
    realm: "$TURN_SUBDOMAIN.$MAIN_DOMAIN"
    listening-port: 3478
    tls-listening-port: 5349
    min-port: 30152
    max-port: 33152
    
    # 内网TURN配置
    external-ip: "auto-detect"
    relay-ip: "auto-detect"
    
    # 认证配置
    use-auth-secret: true
    static-auth-secret: "$(openssl rand -base64 32)"

EOF
}

# 生成自签名证书
generate_self_signed_certificates() {
    if [[ "$SSL_MODE" == "selfsigned" ]]; then
        info "生成自签名SSL证书..."
        
        local cert_dir="$SERVICE_DIR/certs"
        mkdir -p "$cert_dir"
        
        # 生成私钥
        openssl genrsa -out "$cert_dir/tls.key" 2048
        
        # 生成证书签名请求
        openssl req -new -key "$cert_dir/tls.key" -out "$cert_dir/tls.csr" -subj "/CN=$MATRIX_SUBDOMAIN.$MAIN_DOMAIN/O=Matrix Internal/C=CN"
        
        # 生成自签名证书
        openssl x509 -req -in "$cert_dir/tls.csr" -signkey "$cert_dir/tls.key" -out "$cert_dir/tls.crt" -days 365 \
            -extensions v3_req -extfile <(cat <<EOF
[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = $ELEMENT_SUBDOMAIN.$MAIN_DOMAIN
DNS.2 = $MATRIX_SUBDOMAIN.$MAIN_DOMAIN
DNS.3 = $MAS_SUBDOMAIN.$MAIN_DOMAIN
DNS.4 = $RTC_SUBDOMAIN.$MAIN_DOMAIN
DNS.5 = $TURN_SUBDOMAIN.$MAIN_DOMAIN
EOF
)
        
        # 创建Kubernetes Secret
        kubectl create secret tls matrix-tls-secret \
            --cert="$cert_dir/tls.crt" \
            --key="$cert_dir/tls.key" \
            --namespace="$NAMESPACE" \
            --dry-run=client -o yaml | kubectl apply -f -
        
        success "自签名证书生成完成"
    fi
}

# 部署ESS-HELM
deploy_ess_helm() {
    info "开始部署ESS-HELM (内部服务器模式)..."
    
    local config_dir="$SERVICE_DIR/configs"
    local chart_dir="$SERVICE_DIR/ess-helm/matrix-stack"
    
    # 执行Helm部署
    helm upgrade --install "$RELEASE_NAME" "$chart_dir" \
        --namespace "$NAMESPACE" \
        --values "$config_dir/values.yaml" \
        --values "$config_dir/values-internal-server.yaml" \
        --values "$config_dir/values-network.yaml" \
        --timeout 20m \
        --wait
    
    success "ESS-HELM部署完成"
}

# 验证部署
verify_deployment() {
    info "验证内部服务器部署状态..."
    
    # 检查Pod状态
    echo -e "${CYAN}Pod状态:${NC}"
    kubectl get pods -n "$NAMESPACE"
    echo
    
    # 检查服务状态
    echo -e "${CYAN}服务状态:${NC}"
    kubectl get services -n "$NAMESPACE"
    echo
    
    # 检查Ingress状态
    echo -e "${CYAN}Ingress状态:${NC}"
    kubectl get ingress -n "$NAMESPACE"
    echo
    
    # 检查网络策略
    echo -e "${CYAN}网络策略:${NC}"
    kubectl get networkpolicy -n "$NAMESPACE"
    echo
    
    success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    echo
    echo -e "${GREEN}内部服务器部署完成！${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "Element Web: ${WHITE}https://$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT${NC}"
    echo -e "Matrix 服务器: ${WHITE}https://$MATRIX_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT${NC}"
    echo -e "MAS 认证服务: ${WHITE}https://$MAS_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT${NC}"
    echo -e "RTC 服务: ${WHITE}https://$RTC_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT${NC}"
    echo -e "TURN 服务: ${WHITE}$TURN_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo
    echo -e "内网网段: ${WHITE}$INTERNAL_NETWORK${NC}"
    echo -e "SSL模式: ${WHITE}$SSL_MODE${NC}"
    echo -e "服务目录: ${WHITE}$SERVICE_DIR${NC}"
    echo -e "配置文件: ${WHITE}$SERVICE_DIR/configs/${NC}"
    echo -e "日志文件: ${WHITE}$SERVICE_DIR/logs/${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${YELLOW}内网访问说明:${NC}"
    echo "• 服务仅在内网可访问，外网无法直接访问"
    echo "• 使用自签名证书，浏览器可能显示安全警告"
    echo "• 联邦功能已禁用，适用于内部通信"
    echo
    echo -e "${YELLOW}使用管理脚本进行后续管理:${NC}"
    echo -e "  ${WHITE}$PROJECT_DIR/scripts/admin.sh${NC}"
    echo
}

# 复制日志文件到服务目录
copy_logs_to_service_dir() {
    local log_dir="$SERVICE_DIR/logs"
    mkdir -p "$log_dir"

    # 复制所有ESS-HELM相关的临时日志文件
    for log_file in /tmp/ess-helm-*.log /tmp/ess-helm-*.pid; do
        if [[ -f "$log_file" ]]; then
            cp "$log_file" "$log_dir/" 2>/dev/null || true
        fi
    done

    info "日志文件已复制到: $log_dir"
}

# 主函数
main() {
    echo -e "${CYAN}ESS-HELM 内部服务器部署脚本${NC}"
    echo -e "${CYAN}版本: $ESS_HELM_VERSION${NC}"
    echo
    
    # 解析参数
    parse_arguments "$@"
    
    # 验证环境
    validate_environment
    
    # 创建服务目录
    mkdir -p "$SERVICE_DIR"
    
    # 创建命名空间
    create_namespace
    
    # 下载ESS-HELM
    download_ess_helm
    
    # 生成配置文件
    generate_configurations
    
    # 生成自签名证书
    generate_self_signed_certificates
    
    # 部署ESS-HELM
    deploy_ess_helm
    
    # 验证部署
    verify_deployment

    # 复制日志文件到服务目录
    copy_logs_to_service_dir

    # 显示部署信息
    show_deployment_info
}

# 错误处理
trap 'error "部署过程中发生错误"; exit 1' ERR

# 启动主程序
main "$@"
