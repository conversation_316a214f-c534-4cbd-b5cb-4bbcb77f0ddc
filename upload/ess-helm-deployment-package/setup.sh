#!/bin/bash

# Matrix Hybird Deploy Enter
# 版本: v2.1
# 基于: ESS-HELM 25.6.2 官方稳定版 (OCI格式)
# OCI支持: 完全符合Element官方OCI规范
# 作者: AI
# 日期: 2025-06-20

set -euo pipefail

# 临时日志函数（在正式日志函数定义前使用）
temp_info() {
    echo -e "\033[0;34m[信息]\033[0m $1"
}

# 脚本配置 - 支持管道执行
if [[ -n "${BASH_SOURCE[0]:-}" && -f "${BASH_SOURCE[0]}" ]]; then
    # 正常文件执行模式
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    temp_info "使用本地文件执行模式: $SCRIPT_DIR"
else
    # 管道执行模式 - 下载完整项目
    temp_info "检测到管道执行模式，正在下载完整项目..."
    SCRIPT_DIR="$(mktemp -d)"

    # 下载并解压项目文件
    if curl -fsSL https://github.com/niublab/ess/archive/refs/heads/main.tar.gz | tar -xz -C "$SCRIPT_DIR" --strip-components=1 2>/dev/null; then
        temp_info "项目文件下载完成: $SCRIPT_DIR"

        # 设置脚本文件执行权限
        temp_info "设置脚本文件执行权限..."
        find "$SCRIPT_DIR" -name "*.sh" -type f -exec chmod +x {} \; 2>/dev/null || true

        # 验证关键脚本文件权限
        for script in "setup.sh" "scripts/external.sh" "scripts/internal.sh" "scripts/admin.sh"; do
            if [[ -f "$SCRIPT_DIR/$script" ]]; then
                chmod +x "$SCRIPT_DIR/$script" 2>/dev/null || true
                temp_info "✓ 已设置 $script 执行权限"
            fi
        done
    else
        echo "错误: 无法下载项目文件，请检查网络连接"
        exit 1
    fi

    # 设置清理函数
    cleanup_project_dir() {
        if [[ -d "$SCRIPT_DIR" && "$SCRIPT_DIR" =~ ^/tmp/ ]]; then
            rm -rf "$SCRIPT_DIR" 2>/dev/null || true
        fi
    }
    trap cleanup_project_dir EXIT
fi

PROJECT_NAME="ESS-HELM"
VERSION="25.6.2"
LOG_FILE="$(mktemp -t ess-helm-deployment-XXXXXX).log"

# 验证必要文件存在和权限
validate_script_files() {
    local required_scripts=(
        "scripts/external.sh"
        "scripts/internal.sh"
        "scripts/admin.sh"
    )

    temp_info "验证必要脚本文件..."

    for script in "${required_scripts[@]}"; do
        local script_path="$SCRIPT_DIR/$script"

        # 检查文件是否存在
        if [[ ! -f "$script_path" ]]; then
            echo "错误: 缺少必要的脚本文件 $script"
            echo "当前目录: $SCRIPT_DIR"
            echo "文件列表:"
            ls -la "$SCRIPT_DIR" 2>/dev/null || echo "无法列出目录内容"
            if [[ -d "$SCRIPT_DIR/scripts" ]]; then
                echo "scripts目录内容:"
                ls -la "$SCRIPT_DIR/scripts" 2>/dev/null || echo "无法列出scripts目录内容"
            fi
            exit 1
        fi

        # 检查文件是否可执行
        if [[ ! -x "$script_path" ]]; then
            temp_info "设置 $script 执行权限..."
            chmod +x "$script_path" 2>/dev/null || {
                echo "错误: 无法设置 $script 执行权限"
                exit 1
            }
        fi

        # 验证文件不为空
        if [[ ! -s "$script_path" ]]; then
            echo "错误: 脚本文件 $script 为空"
            exit 1
        fi

        temp_info "✓ $script 验证通过"
    done

    temp_info "所有脚本文件验证完成"
}

# 执行文件验证
validate_script_files

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Matrix Hybird Deploy                      ║"
    echo "║                                                              ║"
    echo "║  版本: ${VERSION} (官方稳定版)                                ║"
    echo "║  功能: Router WAN IP检测 + 虚拟公网IP路由 + 增强管理           ║"
    echo "║  支持: Nginx反向代理 + Matrix服务栈 + 完整管理功能              ║"
    echo "║                                                              ║"
    echo "║  🚀 一键部署 | 🛠️ 智能配置 | 🔧 增强管理                     ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# 环境检测函数
check_environment() {
    info "正在检测系统环境..."
    
    # 检测操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        info "检测到操作系统: Linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        info "检测到操作系统: macOS"
    else
        error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检测必需工具 (包含OCI支持要求)
    local required_tools=("curl" "wget" "git" "docker" "kubectl" "helm")
    local missing_tools=()

    info "检查OCI支持要求..."
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        else
            info "✓ $tool 已安装"
            # 特别检查Helm版本是否支持OCI
            if [[ "$tool" == "helm" ]]; then
                local helm_version=$(helm version --short --client 2>/dev/null | grep -oE 'v[0-9]+\.[0-9]+' | sed 's/v//' || echo "0.0")
                local major_version=$(echo "$helm_version" | cut -d. -f1)
                local minor_version=$(echo "$helm_version" | cut -d. -f2)

                if [[ $major_version -lt 3 ]] || [[ $major_version -eq 3 && $minor_version -lt 8 ]]; then
                    warning "Helm版本 $helm_version 不支持OCI，需要3.8+版本"
                    missing_tools+=("helm")
                else
                    info "✓ Helm版本 $helm_version 支持OCI规范"
                fi
            fi
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        warning "以下工具需要安装: ${missing_tools[*]}"
        echo
        echo -e "${YELLOW}是否自动安装缺失的工具? (y/n):${NC}"
        read -r install_tools
        if [[ "$install_tools" =~ ^[Yy]$ ]]; then
            install_missing_tools "${missing_tools[@]}"
        else
            error "请手动安装缺失的工具后重新运行脚本"
            exit 1
        fi
    fi
    
    # 检查OCI仓库连通性
    info "检查Element官方OCI仓库连通性..."
    if curl -sSf https://ghcr.io &>/dev/null; then
        success "✓ 可以访问ghcr.io (Element官方OCI仓库)"
    else
        warning "⚠ 无法访问ghcr.io，部署时可能遇到网络问题"
        warning "请确保网络可以访问GitHub Container Registry"
    fi

    success "环境检测完成"
}

# 安装缺失工具
install_missing_tools() {
    local tools=("$@")
    info "正在安装缺失的工具..."
    
    for tool in "${tools[@]}"; do
        case "$tool" in
            "docker")
                install_docker
                ;;
            "kubectl")
                install_kubectl
                ;;
            "helm")
                install_helm
                ;;
            *)
                if [[ "$OS" == "linux" ]]; then
                    sudo apt-get update && sudo apt-get install -y "$tool"
                elif [[ "$OS" == "macos" ]]; then
                    brew install "$tool"
                fi
                ;;
        esac
    done
}

# 安装Docker
install_docker() {
    info "正在安装 Docker..."
    if [[ "$OS" == "linux" ]]; then
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker "$USER"
        rm get-docker.sh
    elif [[ "$OS" == "macos" ]]; then
        warning "请从 https://docs.docker.com/desktop/mac/install/ 下载并安装 Docker Desktop"
        exit 1
    fi
}

# 安装kubectl
install_kubectl() {
    info "正在安装 kubectl..."
    if [[ "$OS" == "linux" ]]; then
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
        rm kubectl
    elif [[ "$OS" == "macos" ]]; then
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/darwin/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
    fi
}

# 安装Helm (支持OCI)
install_helm() {
    info "正在安装 Helm (需要3.8+版本支持OCI)..."
    curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

    # 验证Helm版本是否支持OCI
    local helm_version=$(helm version --short --client 2>/dev/null | grep -oE 'v[0-9]+\.[0-9]+' | sed 's/v//' || echo "0.0")
    local major_version=$(echo "$helm_version" | cut -d. -f1)
    local minor_version=$(echo "$helm_version" | cut -d. -f2)

    if [[ $major_version -lt 3 ]] || [[ $major_version -eq 3 && $minor_version -lt 8 ]]; then
        warning "安装的Helm版本 $helm_version 可能不支持OCI，建议升级到3.8+"
        warning "部署时将验证OCI支持"
    else
        success "Helm版本 $helm_version 支持OCI规范"
    fi
}

# 配置收集函数
collect_configuration() {
    local deployment_type="$1"
    info "开始收集部署配置信息..."
    echo

    # 主域名配置
    echo -e "${CYAN}请输入您的主域名 (例如: example.com):${NC}"
    read -r MAIN_DOMAIN
    while [[ ! "$MAIN_DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; do
        echo -e "${RED}域名格式不正确，请重新输入:${NC}"
        read -r MAIN_DOMAIN
    done

    # 如果是外部服务器部署，需要收集内部服务器IP
    if [[ "$deployment_type" == "external" ]]; then
        echo
        echo -e "${CYAN}请输入内部Matrix服务器IP地址:${NC}"
        read -r INTERNAL_SERVER_IP
        while [[ ! "$INTERNAL_SERVER_IP" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; do
            echo -e "${RED}IP地址格式不正确，请重新输入:${NC}"
            read -r INTERNAL_SERVER_IP
        done
    fi
    
    # 子域名配置
    echo -e "${CYAN}请配置子域名 (直接回车使用默认值):${NC}"
    echo -e "Element Web 子域名 [默认: element]:"
    read -r ELEMENT_SUBDOMAIN
    ELEMENT_SUBDOMAIN=${ELEMENT_SUBDOMAIN:-element}
    
    echo -e "Matrix 服务器子域名 [默认: matrix]:"
    read -r MATRIX_SUBDOMAIN
    MATRIX_SUBDOMAIN=${MATRIX_SUBDOMAIN:-matrix}
    
    echo -e "MAS 认证服务子域名 [默认: mas]:"
    read -r MAS_SUBDOMAIN
    MAS_SUBDOMAIN=${MAS_SUBDOMAIN:-mas}
    
    echo -e "RTC 服务子域名 [默认: rtc]:"
    read -r RTC_SUBDOMAIN
    RTC_SUBDOMAIN=${RTC_SUBDOMAIN:-rtc}
    
    echo -e "TURN 服务子域名 [默认: turn]:"
    read -r TURN_SUBDOMAIN
    TURN_SUBDOMAIN=${TURN_SUBDOMAIN:-turn}
    
    # 端口配置
    echo -e "${CYAN}请配置服务端口 (直接回车使用默认值):${NC}"
    echo -e "HTTPS 端口 [默认: 8443]:"
    read -r HTTPS_PORT
    HTTPS_PORT=${HTTPS_PORT:-8443}
    
    echo -e "Matrix 联邦端口 [默认: 8448]:"
    read -r MATRIX_FEDERATION_PORT
    MATRIX_FEDERATION_PORT=${MATRIX_FEDERATION_PORT:-8448}
    
    # 服务目录配置
    echo -e "${CYAN}请输入服务主目录路径 [默认: ~/matrix]:${NC}"
    read -r SERVICE_DIR
    SERVICE_DIR=${SERVICE_DIR:-~/matrix}
    SERVICE_DIR=$(eval echo "$SERVICE_DIR")  # 展开 ~ 符号
    
    # SSL证书配置
    echo -e "${CYAN}请选择SSL证书配置方式:${NC}"
    echo "1. Let's Encrypt 自动申请 (推荐)"
    echo "2. 使用自定义证书"
    echo "3. Cloudflare DNS验证 (需要API Token)"
    echo "0. 返回主菜单"
    echo
    echo -e "${YELLOW}请选择 (1-3, 0返回):${NC}"
    read -r ssl_choice

    case "$ssl_choice" in
        1)
            SSL_MODE="letsencrypt"
            info "已选择 Let's Encrypt 自动申请证书"

            # Let's Encrypt 详细配置
            collect_letsencrypt_config
            ;;
        2)
            SSL_MODE="custom"
            echo -e "${CYAN}请输入证书文件路径:${NC}"
            read -r SSL_CERT_PATH
            while [[ ! -f "$SSL_CERT_PATH" ]]; do
                echo -e "${RED}证书文件不存在，请重新输入:${NC}"
                read -r SSL_CERT_PATH
            done

            echo -e "${CYAN}请输入私钥文件路径:${NC}"
            read -r SSL_KEY_PATH
            while [[ ! -f "$SSL_KEY_PATH" ]]; do
                echo -e "${RED}私钥文件不存在，请重新输入:${NC}"
                read -r SSL_KEY_PATH
            done
            ;;
        3)
            SSL_MODE="cloudflare"
            info "已选择 Cloudflare DNS验证"

            # Cloudflare配置
            collect_cloudflare_config
            ;;
        0)
            return 1
            ;;
        *)
            error "无效选择，请重新选择"
            collect_configuration "$1"
            return
            ;;
    esac

    # 显示配置摘要
    show_configuration_summary "$1"
}

# Let's Encrypt 详细配置收集
collect_letsencrypt_config() {
    echo
    echo -e "${CYAN}Let's Encrypt 证书配置:${NC}"

    # 邮箱配置
    echo -e "请输入邮箱地址 (用于证书通知):"
    read -r LETSENCRYPT_EMAIL
    while [[ ! "$LETSENCRYPT_EMAIL" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; do
        echo -e "${RED}邮箱格式不正确，请重新输入:${NC}"
        read -r LETSENCRYPT_EMAIL
    done

    # 证书环境选择
    echo
    echo -e "${CYAN}请选择证书环境:${NC}"
    echo "1. 生产环境 (正式证书，有速率限制)"
    echo "2. 测试环境 (测试证书，无速率限制)"
    echo
    echo -e "${YELLOW}请选择 (1-2):${NC}"
    read -r cert_env_choice

    case "$cert_env_choice" in
        1)
            LETSENCRYPT_ENV="production"
            info "已选择生产环境证书"
            ;;
        2)
            LETSENCRYPT_ENV="staging"
            warning "已选择测试环境证书 (浏览器会显示不受信任)"
            ;;
        *)
            warning "无效选择，默认使用生产环境"
            LETSENCRYPT_ENV="production"
            ;;
    esac

    # 邮箱隐私选择
    echo
    echo -e "${CYAN}邮箱隐私设置:${NC}"
    echo "Let's Encrypt会将您的邮箱地址用于证书相关通知"
    echo -e "${YELLOW}是否同意Let's Encrypt使用您的邮箱地址? (y/n):${NC}"
    read -r email_consent

    if [[ ! "$email_consent" =~ ^[Yy]$ ]]; then
        warning "您选择不同意邮箱使用，将使用匿名模式"
        LETSENCRYPT_EMAIL_CONSENT="false"
        LETSENCRYPT_EMAIL=""
    else
        LETSENCRYPT_EMAIL_CONSENT="true"
        info "已同意邮箱使用条款"
    fi
}

# Cloudflare 配置收集
collect_cloudflare_config() {
    echo
    echo -e "${CYAN}Cloudflare DNS验证配置:${NC}"

    # API Token
    echo -e "请输入Cloudflare API Token:"
    echo -e "${YELLOW}(需要Zone:Read, DNS:Edit权限)${NC}"
    read -r CLOUDFLARE_API_TOKEN
    while [[ -z "$CLOUDFLARE_API_TOKEN" ]]; do
        echo -e "${RED}API Token不能为空，请重新输入:${NC}"
        read -r CLOUDFLARE_API_TOKEN
    done

    # Zone ID (可选)
    echo
    echo -e "请输入Cloudflare Zone ID (可选，留空自动检测):"
    read -r CLOUDFLARE_ZONE_ID

    # 邮箱配置
    echo
    echo -e "请输入Cloudflare账户邮箱:"
    read -r CLOUDFLARE_EMAIL
    while [[ ! "$CLOUDFLARE_EMAIL" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; do
        echo -e "${RED}邮箱格式不正确，请重新输入:${NC}"
        read -r CLOUDFLARE_EMAIL
    done

    info "Cloudflare配置收集完成"
}

# 显示配置摘要
show_configuration_summary() {
    local deployment_type="$1"
    echo
    echo -e "${GREEN}配置摘要:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "主域名: ${WHITE}$MAIN_DOMAIN${NC}"
    if [[ "$deployment_type" == "external" ]]; then
        echo -e "内部服务器IP: ${WHITE}$INTERNAL_SERVER_IP${NC}"
    fi
    echo -e "Element Web: ${WHITE}$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "Matrix 服务器: ${WHITE}$MATRIX_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "MAS 认证服务: ${WHITE}$MAS_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "RTC 服务: ${WHITE}$RTC_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "TURN 服务: ${WHITE}$TURN_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "HTTPS 端口: ${WHITE}$HTTPS_PORT${NC}"
    echo -e "Matrix 联邦端口: ${WHITE}$MATRIX_FEDERATION_PORT${NC}"
    echo -e "服务目录: ${WHITE}$SERVICE_DIR${NC}"
    echo -e "SSL 模式: ${WHITE}$SSL_MODE${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${YELLOW}确认以上配置正确? (y/n):${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        collect_configuration "$deployment_type"
    fi
}

# 主菜单
show_main_menu() {
    while true; do
        clear
        show_welcome
        echo -e "${WHITE}请选择部署模式:${NC}"
        echo
        echo -e "${GREEN}1.${NC} 外部服务部署 (NGINX 反代服务)"
        echo -e "${GREEN}2.${NC} 内部服务部署 (MATRIX 服务栈)"
        echo -e "${GREEN}3.${NC} 管理现有部署 (启动/停止/重启/监控)"
        echo -e "${GREEN}4.${NC} 查看部署状态"
        echo -e "${GREEN}5.${NC} 查看帮助文档"
        echo -e "${GREEN}0.${NC} 退出程序"
        echo
        echo -e "${YELLOW}请选择 (0-5):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                deploy_external_server
                ;;
            2)
                deploy_internal_server
                ;;
            3)
                manage_deployment
                ;;
            4)
                show_deployment_status
                ;;
            5)
                show_help
                ;;
            0)
                echo -e "${GREEN}感谢使用 ESS-HELM 一键部署系统！${NC}"
                exit 0
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 外部服务器部署
deploy_external_server() {
    info "开始外部服务器部署..."
    if collect_configuration "external"; then
        "${SCRIPT_DIR}/scripts/external.sh" \
            --domain "$MAIN_DOMAIN" \
            --internal-server "$INTERNAL_SERVER_IP" \
            --element-subdomain "$ELEMENT_SUBDOMAIN" \
            --matrix-subdomain "$MATRIX_SUBDOMAIN" \
            --mas-subdomain "$MAS_SUBDOMAIN" \
            --rtc-subdomain "$RTC_SUBDOMAIN" \
            --turn-subdomain "$TURN_SUBDOMAIN" \
            --https-port "$HTTPS_PORT" \
            --federation-port "$MATRIX_FEDERATION_PORT" \
            --service-dir "$SERVICE_DIR" \
            --ssl-mode "$SSL_MODE" \
            ${SSL_CERT_PATH:+--ssl-cert "$SSL_CERT_PATH"} \
            ${SSL_KEY_PATH:+--ssl-key "$SSL_KEY_PATH"}
    fi
    
    echo -e "${YELLOW}按任意键返回主菜单...${NC}"
    read -r
}

# 内部服务器部署
deploy_internal_server() {
    info "开始内部服务器部署..."
    if collect_configuration "internal"; then
        "${SCRIPT_DIR}/scripts/internal.sh" \
            --domain "$MAIN_DOMAIN" \
            --element-subdomain "$ELEMENT_SUBDOMAIN" \
            --matrix-subdomain "$MATRIX_SUBDOMAIN" \
            --mas-subdomain "$MAS_SUBDOMAIN" \
            --rtc-subdomain "$RTC_SUBDOMAIN" \
            --turn-subdomain "$TURN_SUBDOMAIN" \
            --https-port "$HTTPS_PORT" \
            --federation-port "$MATRIX_FEDERATION_PORT" \
            --service-dir "$SERVICE_DIR" \
            --ssl-mode "$SSL_MODE" \
            ${SSL_CERT_PATH:+--ssl-cert "$SSL_CERT_PATH"} \
            ${SSL_KEY_PATH:+--ssl-key "$SSL_KEY_PATH"}
    fi
    
    echo -e "${YELLOW}按任意键返回主菜单...${NC}"
    read -r
}

# 管理现有部署
manage_deployment() {
    "${SCRIPT_DIR}/scripts/admin.sh"
}

# 显示部署状态
show_deployment_status() {
    info "正在检查部署状态..."
    "${SCRIPT_DIR}/scripts/admin.sh" --status
    
    echo -e "${YELLOW}按任意键返回主菜单...${NC}"
    read -r
}

# 显示帮助
show_help() {
    clear
    echo -e "${CYAN}ESS-HELM 一键部署系统帮助${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${WHITE}部署模式说明:${NC}"
    echo "• 外部服务部署: 部署Nginx反向代理服务，实现指路牌功能，支持Router WAN IP检测"
    echo "• 内部服务部署: 部署完整的Matrix服务栈，包括Synapse、Element Web、PostgreSQL等"
    echo
    echo -e "${WHITE}核心功能:${NC}"
    echo "• Router WAN IP自动检测 (5秒检测间隔)"
    echo "• 虚拟公网IP路由高可用 (LiveKit: **********, TURN: **********)"
    echo "• 增强管理功能 (用户管理、服务控制、注册控制)"
    echo
    echo -e "${WHITE}技术规范:${NC}"
    echo "• 基于官方 ESS-HELM 25.6.2 稳定版"
    echo "• 支持 Kubernetes + Helm 部署"
    echo "• 完全本地化，无外部服务依赖"
    echo
    echo -e "${WHITE}更多文档:${NC}"
    echo "• 部署指南: docs/deployment-guide.md"
    echo "• 管理指南: docs/admin-guide.md"
    echo "• 故障排除: docs/troubleshooting.md"
    echo
    echo -e "${YELLOW}按任意键返回主菜单...${NC}"
    read -r
}

# 临时文件清理函数
cleanup_temp_files() {
    if [[ -f "$LOG_FILE" ]]; then
        # 复制日志文件到服务目录（如果存在）
        if [[ -n "${SERVICE_DIR:-}" && -d "$SERVICE_DIR" ]]; then
            cp "$LOG_FILE" "$SERVICE_DIR/deployment-$(date +%Y%m%d-%H%M%S).log" 2>/dev/null || true
        fi
        # 保留日志文件，不删除，让用户可以查看
        info "部署日志已保存到: $LOG_FILE"
    fi
}

# 主程序入口
main() {
    # 设置退出时清理临时文件
    trap cleanup_temp_files EXIT

    # 日志文件已通过mktemp创建，无需再次touch
    info "部署日志文件: $LOG_FILE"

    # 检查是否以root权限运行
    if [[ $EUID -eq 0 ]]; then
        warning "不建议以root权限运行此脚本"
    fi

    # 环境检测
    check_environment

    # 显示主菜单
    show_main_menu
}

# 信号处理
trap 'error "脚本被中断"; exit 1' INT TERM

# 启动主程序
main "$@"